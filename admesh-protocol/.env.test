# Test Environment Configuration
# Uses production Firebase/database but exposes on localhost for testing

# Environment
ENV=test
DEBUG=true
LOG_LEVEL=DEBUG
PORT=8000

# Firebase Configuration (production credentials)
GOOGLE_APPLICATION_CREDENTIALS=./firebase/serviceAccountKey.json

# API Configuration (localhost for testing)
SITE_URL=http://127.0.0.1:8000

# External Services
OPENROUTER_API_KEY=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
RESEND_API_KEY=re_EqBocs67_QFgdUbbUEkVvGeommYFp2wXQ

# Test IDs and Keys (using production data)
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
NEXT_PUBLIC_AGENT_API_KEY=sk_test_IFTLcrkWf2Hx9GUfb6pNXwaMpJ4GryRw

# Security
JWT_SECRET=test-jwt-secret-key-for-local-testing

# Performance
MAX_WORKERS=4
REQUEST_TIMEOUT=30

# Monitoring (optional for test)
# SENTRY_DSN=your_sentry_dsn_here
