#!/usr/bin/env python3
"""
Migration script to move GEO analyses from top-level collection to brand subcollections.

This script moves documents from:
  geo_analyses/{analysisId}
To:
  brands/{brandId}/geo_analyses/{analysisId}

Usage:
  python scripts/migrate_geo_analyses.py [--dry-run] [--batch-size=50]
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from firebase.config import get_db

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def migrate_geo_analyses(dry_run=False, batch_size=50):
    """
    Migrate GEO analyses from top-level collection to brand subcollections.
    
    Args:
        dry_run (bool): If True, only log what would be done without making changes
        batch_size (int): Number of documents to process in each batch
    """
    try:
        db = get_db()
        
        # Get all documents from the old geo_analyses collection
        old_collection_ref = db.collection("geo_analyses")
        all_docs = list(old_collection_ref.stream())
        
        logger.info(f"Found {len(all_docs)} GEO analyses to migrate")
        
        if len(all_docs) == 0:
            logger.info("No documents to migrate")
            return
        
        migrated_count = 0
        error_count = 0
        
        # Process documents in batches
        for i in range(0, len(all_docs), batch_size):
            batch = all_docs[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: documents {i+1} to {min(i+batch_size, len(all_docs))}")
            
            for doc in batch:
                try:
                    doc_id = doc.id
                    data = doc.to_dict()
                    brand_id = data.get("brand_id")
                    
                    if not brand_id:
                        logger.warning(f"Document {doc_id} has no brand_id, skipping")
                        error_count += 1
                        continue
                    
                    # Check if brand exists
                    brand_ref = db.collection("brands").document(brand_id)
                    brand_doc = brand_ref.get()
                    
                    if not brand_doc.exists:
                        logger.warning(f"Brand {brand_id} does not exist for document {doc_id}, skipping")
                        error_count += 1
                        continue
                    
                    if dry_run:
                        logger.info(f"[DRY RUN] Would migrate {doc_id} to brands/{brand_id}/geo_analyses/{doc_id}")
                    else:
                        # Create document in new location
                        new_doc_ref = brand_ref.collection("geo_analyses").document(doc_id)
                        new_doc_ref.set(data)
                        
                        # Delete from old location
                        doc.reference.delete()
                        
                        logger.info(f"Migrated {doc_id} to brands/{brand_id}/geo_analyses/{doc_id}")
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.error(f"Error migrating document {doc.id}: {str(e)}")
                    error_count += 1
                    continue
        
        # Summary
        logger.info(f"Migration completed:")
        logger.info(f"  - Successfully migrated: {migrated_count}")
        logger.info(f"  - Errors: {error_count}")
        logger.info(f"  - Total processed: {len(all_docs)}")
        
        if dry_run:
            logger.info("This was a dry run. No actual changes were made.")
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        raise


def verify_migration():
    """
    Verify that the migration was successful by checking both collections.
    """
    try:
        db = get_db()
        
        # Check old collection
        old_collection_ref = db.collection("geo_analyses")
        old_docs = list(old_collection_ref.stream())
        
        logger.info(f"Documents remaining in old collection: {len(old_docs)}")
        
        # Check new subcollections
        brands_ref = db.collection("brands")
        total_new_docs = 0
        
        for brand_doc in brands_ref.stream():
            brand_id = brand_doc.id
            geo_analyses_ref = brand_doc.reference.collection("geo_analyses")
            geo_docs = list(geo_analyses_ref.stream())
            
            if len(geo_docs) > 0:
                logger.info(f"Brand {brand_id}: {len(geo_docs)} GEO analyses")
                total_new_docs += len(geo_docs)
        
        logger.info(f"Total documents in new subcollections: {total_new_docs}")
        
    except Exception as e:
        logger.error(f"Verification failed: {str(e)}")
        raise


def main():
    parser = argparse.ArgumentParser(description="Migrate GEO analyses to brand subcollections")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--batch-size", type=int, default=50, help="Number of documents to process in each batch")
    parser.add_argument("--verify", action="store_true", help="Verify migration results")
    
    args = parser.parse_args()
    
    if args.verify:
        logger.info("Verifying migration results...")
        verify_migration()
    else:
        logger.info(f"Starting GEO analyses migration (dry_run={args.dry_run})")
        migrate_geo_analyses(dry_run=args.dry_run, batch_size=args.batch_size)


if __name__ == "__main__":
    main()
