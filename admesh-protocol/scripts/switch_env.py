#!/usr/bin/env python3
"""
Environment switching script for AdMesh Protocol
Switches between development and production environments
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


def get_project_root():
    """Get the project root directory"""
    return Path(__file__).parent.parent


def switch_environment(env: str):
    """Switch to the specified environment"""
    project_root = get_project_root()

    # Environment configuration
    environments = {
        'dev': {
            'file': '.env.dev',
            'name': 'Development',
            'description': 'Local development with debugging enabled',
            'expected_debug': 'true',
            'expected_log_level': 'DEBUG'
        },
        'development': {
            'file': '.env.dev',
            'name': 'Development',
            'description': 'Local development with debugging enabled',
            'expected_debug': 'true',
            'expected_log_level': 'DEBUG'
        },

        'prod': {
            'file': '.env.production',
            'name': 'Production',
            'description': 'Live deployment configuration',
            'expected_debug': 'false',
            'expected_log_level': 'WARNING'
        },
        'production': {
            'file': '.env.production',
            'name': 'Production',
            'description': 'Live deployment configuration',
            'expected_debug': 'false',
            'expected_log_level': 'WARNING'
        }
    }

    if env not in environments:
        print(f"❌ Invalid environment: {env}")
        print("Available environments:")
        for key, config in environments.items():
            if key not in ['development', 'production']:  # Don't show duplicates
                print(f"  {key.ljust(4)} - {config['name']}: {config['description']}")
        return False

    config = environments[env]
    source_file = project_root / config['file']
    target_file = project_root / '.env'

    if not source_file.exists():
        print(f"❌ Environment file not found: {source_file}")
        return False

    try:
        # Copy the environment file
        shutil.copy2(source_file, target_file)

        # Read and validate the environment file
        with open(target_file, 'r') as f:
            content = f.read()

        # Extract key information
        env_value = None
        site_url = None
        debug = None
        log_level = None
        port = None

        for line in content.split('\n'):
            if line.startswith('ENV='):
                env_value = line.split('=', 1)[1]
            elif line.startswith('SITE_URL='):
                site_url = line.split('=', 1)[1]
            elif line.startswith('DEBUG='):
                debug = line.split('=', 1)[1]
            elif line.startswith('LOG_LEVEL='):
                log_level = line.split('=', 1)[1]
            elif line.startswith('PORT='):
                port = line.split('=', 1)[1]

        print('✅ Environment switched successfully!')
        print(f'🌍 Environment: {config["name"]} ({env_value})')
        print(f'🔗 Site URL: {site_url}')
        print(f'🐛 Debug: {debug}')
        print(f'📊 Log Level: {log_level}')
        print(f'🚪 Port: {port}')
        print(f'📝 Description: {config["description"]}')
        print('')

        # Validate configuration
        warnings = []
        if debug != config['expected_debug']:
            warnings.append(f"Debug mode mismatch. Expected: {config['expected_debug']}, Got: {debug}")
        if log_level != config['expected_log_level']:
            warnings.append(f"Log level mismatch. Expected: {config['expected_log_level']}, Got: {log_level}")

        if warnings:
            print('⚠️  Configuration warnings:')
            for warning in warnings:
                print(f'   - {warning}')
            print('')

        print('Available commands:')
        if env in ['dev', 'development']:
            print('  python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000  # Development server')
        else:
            print('  python -m uvicorn api.main:app --host 0.0.0.0 --port 8000           # Production server')
        print('  ./run_dev.sh                                                        # Development script')
        print('  ./run_prod.sh                                                       # Production script')
        print('')
        print('Environment switching:')
        print('  python scripts/switch_env.py dev    - Switch to development')
        print('  python scripts/switch_env.py prod   - Switch to production')

        return True

    except Exception as e:
        print(f'❌ Error switching environment: {e}')
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Switch AdMesh Protocol environment')
    parser.add_argument('environment',
                       choices=['dev', 'development', 'prod', 'production'],
                       help='Environment to switch to')
    
    args = parser.parse_args()
    
    print(f"🔄 Switching to {args.environment} environment...")
    
    if switch_environment(args.environment):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
