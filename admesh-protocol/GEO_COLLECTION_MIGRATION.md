# GEO Analyses Collection Migration

## Overview

This document describes the migration of GEO analyses from a top-level collection to brand subcollections for better data organization and security.

## Changes Made

### **Before (Old Structure)**
```
geo_analyses/
├── {analysisId1}/
│   ├── brand_id: "brandId1"
│   ├── overall_score: 85
│   └── ... (other analysis data)
├── {analysisId2}/
│   ├── brand_id: "brandId2"
│   └── ... (other analysis data)
└── ...
```

### **After (New Structure)**
```
brands/
├── {brandId1}/
│   ├── geo_analyses/
│   │   ├── {analysisId1}/
│   │   │   ├── overall_score: 85
│   │   │   └── ... (analysis data, no brand_id needed)
│   │   └── {analysisId2}/
│   └── ... (other brand data)
├── {brandId2}/
│   ├── geo_analyses/
│   │   └── {analysisId3}/
│   └── ... (other brand data)
└── ...
```

## Benefits

1. **Better Data Organization**: Each brand's analyses are grouped under their document
2. **Improved Security**: Natural access control through Firestore security rules
3. **Simplified Queries**: No need to filter by `brand_id` in queries
4. **Scalability**: Better performance for brands with many analyses
5. **Consistency**: Follows the same pattern as other brand-related subcollections

## API Endpoint Changes

### **Storage (POST /geo/check)**
- **Before**: `db.collection("geo_analyses").add(analysis_result)`
- **After**: `db.collection("brands").document(brand_id).collection("geo_analyses").add(analysis_result)`

### **History (GET /geo/history)**
- **Before**: `db.collection("geo_analyses").where("brand_id", "==", brand_id)`
- **After**: `db.collection("brands").document(brand_id).collection("geo_analyses")`

### **Specific Analysis (GET /geo/history/{analysis_id})**
- **Before**: `db.collection("geo_analyses").document(analysis_id)`
- **After**: `db.collection("brands").document(brand_id).collection("geo_analyses").document(analysis_id)`

## Migration Process

### **1. Automatic Migration Script**
```bash
# Dry run to see what would be migrated
cd admesh-protocol
python scripts/migrate_geo_analyses.py --dry-run

# Actual migration
python scripts/migrate_geo_analyses.py

# Verify migration results
python scripts/migrate_geo_analyses.py --verify
```

### **2. Migration Script Features**
- **Dry Run Mode**: Preview changes without making them
- **Batch Processing**: Handles large datasets efficiently
- **Error Handling**: Continues processing even if some documents fail
- **Verification**: Check migration results
- **Logging**: Detailed progress and error reporting

### **3. Safety Measures**
- Documents are copied to new location before deletion
- Brand existence is verified before migration
- Comprehensive error handling and logging
- Rollback capability (manual if needed)

## Testing

### **Test Environment Setup**
The changes have been tested in the test environment which:
- Uses production Firebase credentials (`admesh-9560c`)
- Runs API on localhost (`127.0.0.1:8000`)
- Allows testing with real production data structure

### **Verification Steps**
1. ✅ API endpoints updated to use subcollections
2. ✅ History fetching works with new structure
3. ✅ New analyses are stored in correct location
4. ✅ Migration script created and tested
5. ✅ Backward compatibility considerations addressed

## Deployment Steps

### **1. Pre-Deployment**
- [ ] Run migration script in dry-run mode
- [ ] Verify all existing analyses have valid `brand_id`
- [ ] Backup existing `geo_analyses` collection

### **2. Deployment**
- [ ] Deploy updated API code
- [ ] Run migration script
- [ ] Verify migration results
- [ ] Test API endpoints

### **3. Post-Deployment**
- [ ] Monitor for any issues
- [ ] Clean up old collection (after verification)
- [ ] Update any external integrations

## Rollback Plan

If issues arise, rollback steps:

1. **Revert API Code**: Deploy previous version
2. **Restore Data**: Copy documents back to top-level collection
3. **Verify**: Test all functionality

## Security Considerations

### **Firestore Security Rules**
Update rules to allow access to subcollections:

```javascript
// Allow brands to read/write their own geo_analyses
match /brands/{brandId}/geo_analyses/{analysisId} {
  allow read, write: if request.auth != null && request.auth.uid == brandId;
}
```

## Performance Impact

### **Positive Impacts**
- Faster queries (no need to filter by brand_id)
- Better indexing performance
- Reduced data transfer for large brands

### **Considerations**
- Slightly more complex query paths
- Need to know brand_id for direct access

## Monitoring

Monitor these metrics post-migration:
- API response times for `/geo/history`
- Error rates on GEO endpoints
- Database query performance
- User-reported issues

## Support

For issues related to this migration:
1. Check migration logs
2. Verify brand_id in requests
3. Ensure proper authentication
4. Review Firestore security rules
