# AdMesh Test Environment Setup

This document describes how to set up and use the AdMesh test environment, which uses production Firebase/database but exposes the API on localhost for testing purposes.

## Overview

The test environment provides:
- **Production Firebase credentials** and database access
- **Localhost API** (`http://127.0.0.1:8000`) for easy testing
- **Production data** for realistic testing scenarios
- **Debug logging** enabled for development

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Dashboard (Test)  │    │   Backend (Test)    │    │  Firebase (Prod)    │
│                     │    │                     │    │                     │
│ • Production        │───▶│ • Production DB     │───▶│ • admesh-9560c      │
│   Firebase Config   │    │ • Localhost API     │    │ • Production data   │
│ • Calls localhost   │    │ • Debug enabled     │    │ • Real users/brands │
│   API               │    │ • Port 8000         │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Prerequisites

1. **Production Firebase credentials** must be available at:
   ```
   admesh-protocol/firebase/serviceAccountKey.json
   ```

2. **Required environment variables** in `.env.test`:
   - `OPENROUTER_API_KEY`
   - `RESEND_API_KEY`

## Quick Start

### 1. Start Backend (Test Environment)

```bash
cd admesh-protocol
./run_test.sh
```

This will:
- Load production Firebase credentials
- Connect to production database (`admesh-9560c`)
- Expose API on `http://127.0.0.1:8000`
- Enable debug logging

### 2. Start Dashboard (Test Environment)

```bash
cd admesh-dashboard
./run_test.sh
```

This will:
- Use production Firebase configuration
- Call localhost API (`http://127.0.0.1:8000`)
- Start dashboard on `http://localhost:3000`

## Environment Files

### Backend: `.env.test`
```bash
ENV=test
DEBUG=true
LOG_LEVEL=DEBUG
PORT=8000
GOOGLE_APPLICATION_CREDENTIALS=./firebase/serviceAccountKey.json
SITE_URL=http://127.0.0.1:8000
# ... other variables
```

### Dashboard: `.env.test`
```bash
NEXT_PUBLIC_ENVIRONMENT=test
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000
NEXT_PUBLIC_FIREBASE_PROJECT_ID=admesh-9560c
# ... other Firebase production config
```

## Configuration Details

### Backend Configuration (`config/test.py`)
- **Firebase**: Production project (`admesh-9560c`)
- **Database**: Production Firestore
- **API**: Localhost (`127.0.0.1:8000`)
- **CORS**: Allows localhost origins
- **Debug**: Enabled with full logging

### Dashboard Configuration (`src/config/environment.ts`)
- **Firebase**: Production credentials
- **API**: Localhost backend
- **Features**: Analytics enabled, debugging enabled

## Use Cases

### 1. Testing with Real Data
- Test features with actual production data
- Validate API responses with real user/brand data
- Debug issues that only occur with production data

### 2. Local Development with Production Context
- Develop new features against production database
- Test integrations without affecting production API
- Debug production-specific issues locally

### 3. API Testing
- Test API endpoints with production data
- Validate database queries and responses
- Performance testing with real data volume

## Security Considerations

⚠️ **Important Security Notes:**

1. **Production Data Access**: This environment accesses production data. Use responsibly.
2. **Local Only**: Never expose the test environment publicly.
3. **Credentials**: Keep production Firebase credentials secure.
4. **Data Modification**: Be careful when testing write operations.

## Troubleshooting

### Backend Issues

1. **Firebase Credentials Not Found**
   ```bash
   ❌ Error: Production Firebase credentials not found!
   ```
   **Solution**: Ensure `./firebase/serviceAccountKey.json` exists

2. **Port Already in Use**
   ```bash
   Error: Port 8000 is already in use
   ```
   **Solution**: Kill existing process or change port in `.env.test`

3. **Environment Variables Missing**
   ```bash
   Missing required environment variables for test
   ```
   **Solution**: Check `.env.test` file has all required variables

### Dashboard Issues

1. **API Connection Failed**
   ```bash
   Failed to fetch from http://127.0.0.1:8000
   ```
   **Solution**: Ensure backend is running first

2. **Firebase Configuration Error**
   ```bash
   Firebase configuration error
   ```
   **Solution**: Verify production Firebase config in `.env.test`

## Manual Setup (Alternative)

If you prefer manual setup instead of using the scripts:

### Backend
```bash
cd admesh-protocol
export ENV=test
export $(cat .env.test | grep -v '^#' | xargs)
uvicorn api.main:app --host 127.0.0.1 --port 8000 --reload --env-file .env.test
```

### Dashboard
```bash
cd admesh-dashboard
export NEXT_PUBLIC_ENVIRONMENT=test
export $(cat .env.test | grep -v '^#' | xargs)
npm run dev
```

## Monitoring

When running in test mode, you'll see:
- **Backend**: Debug logs with database queries and API calls
- **Dashboard**: Environment indicator showing "test" mode
- **Console**: Firebase project ID and API URL confirmation

## Next Steps

After setting up the test environment:
1. Verify both services are running
2. Test API endpoints at `http://127.0.0.1:8000/docs`
3. Access dashboard at `http://localhost:3000`
4. Check logs for any configuration issues
